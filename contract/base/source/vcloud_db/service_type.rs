// Service type module implementation

impl VCloudDB {
    /// Create a new service type from JSON string
    pub fn insert_service_type(&mut self, service_type_json: String) -> anyhow::Result<String> {
        let mut service_type: ServiceType = serde_json::from_str(&service_type_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse service type JSON: {}", e))?;
        
        // Validate required fields
        if service_type._id.is_empty() {
            return Err(anyhow::anyhow!("Service type ID cannot be empty"));
        }

        if self.service_types.contains(&service_type._id) {
            return Err(anyhow::anyhow!("Service type with this ID already exists"));
        }

        // Apply timestamp handling logic
        self.apply_service_type_timestamp_handling(&mut service_type);

        // Ensure deleted_at is 0 for new service types
        service_type.deleted_at = 0;

        self.service_types.insert(&service_type._id, &service_type);
        Ok(service_type._id)
    }

    /// Create multiple service types from JSON array string
    pub fn insert_many_service_type(&mut self, service_types_json: String) -> anyhow::Result<String> {
        let service_types: Vec<ServiceType> = serde_json::from_str(&service_types_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse service types JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for mut service_type in service_types {
            // Validate required fields
            if service_type._id.is_empty() {
                result.errors.push("Service type ID cannot be empty".to_string());
                continue;
            }

            if self.service_types.contains(&service_type._id) {
                result.errors.push(format!("Service type with ID '{}' already exists", service_type._id));
                continue;
            }

            // Apply timestamp handling logic
            self.apply_service_type_timestamp_handling(&mut service_type);

            // Ensure deleted_at is 0 for new service types
            service_type.deleted_at = 0;

            // Insert the service type
            self.service_types.insert(&service_type._id, &service_type);
            result.created += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Get a single service type by ID
    pub fn get_service_type(&self, id: String) -> anyhow::Result<String> {
        let service_type = self.service_types.get(&id);
        match service_type {
            Some(service_type) => Ok(serde_json::to_string(&service_type)?),
            None => Err(anyhow::anyhow!("not found")),
        }
    }

    /// Update an existing service type from JSON string
    pub fn update_service_type(&mut self, service_type_json: String) -> anyhow::Result<()> {
        let mut service_type: ServiceType = serde_json::from_str(&service_type_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse service type JSON: {}", e))?;

        // Validate required fields
        if service_type._id.is_empty() {
            return Err(anyhow::anyhow!("Service type ID cannot be empty"));
        }

        if !self.service_types.contains(&service_type._id) {
            return Err(anyhow::anyhow!("Service type not found"));
        }

        // Apply timestamp handling logic for updates
        if service_type.updated_at == 0 {
            service_type.updated_at = self.get_current_timestamp();
        }

        self.service_types.insert(&service_type._id, &service_type);
        Ok(())
    }

    /// Apply timestamp handling logic based on input values
    pub(crate) fn apply_service_type_timestamp_handling(&self, service_type: &mut ServiceType) {
        let current_time = self.get_current_timestamp();

        // For created_at: if input is 0, set to current timestamp
        if service_type.created_at == 0 {
            service_type.created_at = current_time;
        }

        // For updated_at: if input is 0, set to current timestamp
        if service_type.updated_at == 0 {
            service_type.updated_at = current_time;
        }

        // For deleted_at: if input is 0, keep it as 0 (not deleted)
        // No action needed as 0 means not deleted
    }

    /// Update multiple service types matching the filter
    pub fn update_many_service_type(&mut self, update_json: String) -> anyhow::Result<String> {
        let update_params: ServiceTypeUpdate = serde_json::from_str(&update_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse ServiceTypeUpdate JSON: {}", e))?;

        let params = update_params.filter;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        // Find service types matching the filter criteria
        let mut service_types_to_update = Vec::new();

        if let Some(ref ids) = params.ids {
            for id in ids {
                if let Some(service_type) = self.service_types.get(id) {
                    if self.matches_service_type_filters(&service_type, &params) {
                        service_types_to_update.push(service_type);
                    }
                }
            }
        } else {
            let mut iter = self.service_types.index("service_types_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let service_type = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service type: {}", e))?;
                if self.matches_service_type_filters(&service_type, &params) {
                    service_types_to_update.push(service_type);
                }
            }
        }

        // Update each matching service type with partial data
        for mut service_type in service_types_to_update {
            // Parse update data as a generic JSON object for partial updates
            if let Some(update_obj) = update_params.update_data.as_object() {
                // Update only specified fields
                for (key, value) in update_obj {
                    match key.as_str() {
                        "updatedAt" => {
                            if let Some(updated_at) = value.as_i64() {
                                service_type.updated_at = updated_at;
                            }
                        }
                        "deletedAt" => {
                            if let Some(deleted_at) = value.as_i64() {
                                service_type.deleted_at = deleted_at;
                            }
                        }
                        "name" => {
                            if let Some(name) = value.as_str() {
                                service_type.name = name.to_string();
                            }
                        }
                        "provider" => {
                            if let Some(provider) = value.as_str() {
                                service_type.provider = provider.to_string();
                            }
                        }
                        "refundable" => {
                            if let Some(refundable) = value.as_bool() {
                                service_type.refundable = refundable;
                            }
                        }
                        "categoryID" => {
                            if let Some(category_id) = value.as_str() {
                                service_type.category_id = category_id.to_string();
                            }
                        }
                        "category" => {
                            if let Some(category) = value.as_str() {
                                service_type.category = category.to_string();
                            }
                        }
                        "serviceOptions" => {
                            if let Some(service_options_obj) = value.as_object() {
                                let mut service_options = std::collections::HashMap::new();
                                for (key, val) in service_options_obj {
                                    if let Some(array) = val.as_array() {
                                        let string_vec: Vec<String> = array
                                            .iter()
                                            .filter_map(|v| v.as_str().map(|s| s.to_string()))
                                            .collect();
                                        service_options.insert(key.clone(), string_vec);
                                    }
                                }
                                service_type.service_options = service_options;
                            }
                        }
                        "description" => {
                            if let Some(description) = value.as_str() {
                                service_type.description = description.to_string();
                            }
                        }
                        "apiHost" => {
                            if let Some(api_host) = value.as_str() {
                                service_type.api_host = api_host.to_string();
                            }
                        }
                        "durationToPrice" => {
                            if let Some(duration_to_price_array) = value.as_array() {
                                let mut price_sets = Vec::new();
                                for price_set_value in duration_to_price_array {
                                    if let Ok(price_set) = serde_json::from_value::<PriceSet>(price_set_value.clone()) {
                                        price_sets.push(price_set);
                                    }
                                }
                                service_type.duration_to_price = price_sets;
                            }
                        }
                        "serviceOptionDesc" => {
                            if let Some(service_option_desc_obj) = value.as_object() {
                                let mut service_option_desc = std::collections::HashMap::new();
                                for (key, val) in service_option_desc_obj {
                                    if let Some(inner_obj) = val.as_object() {
                                        let mut inner_map = std::collections::HashMap::new();
                                        for (inner_key, inner_val) in inner_obj {
                                            if let Some(inner_str) = inner_val.as_str() {
                                                inner_map.insert(inner_key.clone(), inner_str.to_string());
                                            }
                                        }
                                        service_option_desc.insert(key.clone(), inner_map);
                                    }
                                }
                                service_type.service_option_desc = service_option_desc;
                            }
                        }
                        
                        // Add more fields as needed
                        _ => {} // Ignore unknown fields
                    }
                }
            }

            self.service_types.insert(&service_type._id, &service_type);
            result.updated += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Bulk write operations for service types
    pub fn bulk_write_service_type(&mut self, operations_json: String) -> anyhow::Result<String> {
        let operations: Vec<ServiceTypeBulkWriteOperation> = serde_json::from_str(&operations_json)
            .map_err(|e| anyhow::anyhow!("Failed to parse bulk write operations JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        for operation in operations {
            match operation.operation_type.as_str() {
                "insert" => {
                    if let Some(data) = operation.data {
                        match serde_json::from_value::<ServiceType>(data) {
                            Ok(mut service_type) => {
                                if service_type._id.is_empty() {
                                    result.errors.push("Service type ID cannot be empty".to_string());
                                    continue;
                                }
                                if self.service_types.contains(&service_type._id) {
                                    result.errors.push(format!("Service type with ID '{}' already exists", service_type._id));
                                    continue;
                                }
                                self.apply_service_type_timestamp_handling(&mut service_type);
                                service_type.deleted_at = 0;
                                self.service_types.insert(&service_type._id, &service_type);
                                result.created += 1;
                            }
                            Err(e) => {
                                result.errors.push(format!("Failed to parse service type data: {}", e));
                            }
                        }
                    }
                }
                "update" => {
                    if let (Some(filter), Some(data)) = (operation.filter, operation.data) {
                        let update_params = ServiceTypeUpdate {
                            filter,
                            update_data: data,
                        };
                        let update_json = serde_json::to_string(&update_params)?;
                        match self.update_many_service_type(update_json) {
                            Ok(update_result) => {
                                if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&update_result) {
                                    result.updated += batch_result.updated;
                                    result.errors.extend(batch_result.errors);
                                }
                            }
                            Err(e) => {
                                result.errors.push(format!("Update operation failed: {}", e));
                            }
                        }
                    }
                }
                "delete_many" => {
                    if let Some(filter) = operation.filter {
                        let delete_result = self.delete_many_service_type(serde_json::to_string(&filter)?)?;
                        if let Ok(delete_batch_result) = serde_json::from_str::<BatchResult>(&delete_result) {
                            result.deleted += delete_batch_result.deleted;
                        }
                    }
                }
                _ => {
                    result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                }
            }
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Internal implementation for deleting a single service type (HARD DELETE)
    pub(crate) fn delete_service_type(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: ServiceTypeQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse ServiceTypeQueryParams JSON: {}", e))?;

        // If IDs are provided, delete the first matching one
        if let Some(ref ids) = params.ids {
            if let Some(id) = ids.first() {
                if self.service_types.contains(id) {
                    self.service_types.remove(id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }
        }

        // Find the first matching service type
        let mut iter = self.service_types.index("service_types_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let service_type = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service type: {}", e))?;
            if self.matches_service_type_filters(&service_type, &params) {
                // Hard delete: completely remove from storage
                self.service_types.remove(&service_type._id);
                return Ok(format!("{{\"deleted\": 1}}"));
            }
        }

        Ok(format!("{{\"deleted\": 0}}"))
    }

    /// Internal implementation for batch deleting service types
    pub(crate) fn delete_many_service_type(&mut self, filter_json_string: String) -> anyhow::Result<String> {
        let params: ServiceTypeQueryParams = serde_json::from_str(&filter_json_string)
            .map_err(|e| anyhow::anyhow!("Failed to parse ServiceTypeQueryParams JSON: {}", e))?;

        let mut result = BatchResult {
            created: 0,
            updated: 0,
            deleted: 0,
            errors: Vec::new(),
        };

        let mut service_types_to_delete = Vec::new();

        // Find all matching service types
        let mut iter = self.service_types.index("service_types_created_at").iter(
            false,
            &format!("{:0>19}", 0),
            &format!("{:9>19}", i64::MAX)
        );
        while iter.next() {
            let service_type = iter.value()?;
            if self.matches_service_type_filters(&service_type, &params) {
                service_types_to_delete.push(service_type);
            }
        }

        // Delete each matching service type (HARD DELETE)
        for service_type in service_types_to_delete {
            // Hard delete: completely remove from storage
            self.service_types.remove(&service_type._id);
            result.deleted += 1;
        }

        Ok(serde_json::to_string(&result)?)
    }

    /// Enhanced query service types with comprehensive filtering, pagination, and sorting
    pub fn find_service_type(&self, params_json: String) -> anyhow::Result<String> {
        let params: ServiceTypeQueryParams = serde_json::from_str(&params_json)?;

        let mut service_types = Vec::new();
        let mut count = 0u64;
        let limit = params.limit.unwrap_or(u64::MAX);
        let offset = params.offset.unwrap_or(0);

        // Determine the most efficient index to use based on provided filters
        // Priority: Handle batch ID queries first, then use composite indexes when multiple filters are present
        if let Some(ref ids) = params.ids {
            // Batch ID query - fetch service types by IDs directly
            for id in ids {
                if let Some(service_type) = self.service_types.get(id) {
                    if self.matches_service_type_filters(&service_type, &params) {
                        if count < offset {
                            count += 1;
                            continue;
                        }
                        if service_types.len() >= limit as usize {
                            break;
                        }
                        service_types.push(service_type);
                        count += 1;
                    }
                }
            }
        } else if let Some(ref provider) = params.provider {
            // Use provider index with efficient sorting
            self.query_service_type_with_index("service_types_provider", provider, &params, &mut service_types, &mut count, limit, offset)?;
        } else if let Some(ref category) = params.category {
            // Use category index with efficient sorting
            self.query_service_type_with_index("service_types_category", category, &params, &mut service_types, &mut count, limit, offset)?;
        } else if let Some(ref name) = params.name {
            // Use name index with efficient sorting
            self.query_service_type_with_index("service_types_name", name, &params, &mut service_types, &mut count, limit, offset)?;
        } else if let Some(ref names) = params.names {
            // Use status index
            for name in names {
                self.query_service_type_with_index("service_types_name", name, &params, &mut service_types, &mut count, limit, offset)?;
                if service_types.len() >= limit as usize {
                    break;
                }
            }
        }else {
            // No specific index, use created_at index with efficient sorting
            self.query_service_type_with_created_at(&params, &mut service_types, &mut count, limit, offset)?;
        }
        // Note: Sorting is now handled efficiently during iteration, no post-processing needed
        Ok(serde_json::to_string(&service_types)?)
    }

    /// Count service types with advanced filtering
    pub fn count_service_type(&self, params_json: String) -> anyhow::Result<String> {
        let params: ServiceTypeQueryParams = serde_json::from_str(&params_json)?;

        let mut count = 0u64;

        // Determine the most efficient index to use based on provided filters
        // Priority: Handle batch ID queries first, then use composite indexes when multiple filters are present
        if let Some(ref ids) = params.ids {
            // Batch ID query - fetch service types by IDs directly
            for id in ids {
                if let Some(service_type) = self.service_types.get(id) {
                    if self.matches_service_type_filters(&service_type, &params) {
                        count += 1;
                    }
                }
            }
        } else if let Some(ref provider) = params.provider {
            // Use provider index for efficient counting
            self.count_service_type_with_index("service_types_provider", provider, &params, &mut count)?;
        } else if let Some(ref category) = params.category {
            // Use category index for efficient counting
            self.count_service_type_with_index("service_types_category", category, &params, &mut count)?;
        } else if let Some(ref name) = params.name {
            // Use name index for efficient counting
            self.count_service_type_with_index("service_types_name", name, &params, &mut count)?;
        } else {
            // No specific index, iterate through all service types using created_at index
            let mut iter = self.service_types.index("service_types_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let service_type = iter.value()?;
                if self.matches_service_type_filters(&service_type, &params) {
                    count += 1;
                }
            }
        }

        Ok(count.to_string())
    }

    /// Helper function to check if a service type matches the given filters
    pub(crate) fn matches_service_type_filters(&self, service_type: &ServiceType, params: &ServiceTypeQueryParams) -> bool {
        // Skip deleted service types unless specifically querying for them
        if service_type.deleted_at > 0 {
            return false;
        }

        // Check IDs filter (batch ID query)
        if let Some(ref ids) = params.ids {
            if !ids.contains(&service_type._id) {
                return false;
            }
        }

        // Check name filter
        if let Some(ref name) = params.name {
            if service_type.name != *name {
                return false;
            }
        }

        // Check provider filter
        if let Some(ref provider) = params.provider {
            if service_type.provider != *provider {
                return false;
            }
        }

        // Check category filter
        if let Some(ref category) = params.category {
            if service_type.category != *category {
                return false;
            }
        }

        // Check category_id filter
        if let Some(ref category_id) = params.category_id {
            if service_type.category_id != *category_id {
                return false;
            }
        }

        // Check refundable filter
        if let Some(refundable) = params.refundable {
            if service_type.refundable != refundable {
                return false;
            }
        }

        // Check created_at range
        if let Some(start) = params.created_at_start {
            if service_type.created_at < start {
                return false;
            }
        }
        if let Some(end) = params.created_at_end {
            if service_type.created_at > end {
                return false;
            }
        }

        // Check updated_at range
        if let Some(start) = params.updated_at_start {
            if service_type.updated_at < start {
                return false;
            }
        }
        if let Some(end) = params.updated_at_end {
            if service_type.updated_at > end {
                return false;
            }
        }

        // Check names filter (new field added per requirements)
        if let Some(ref names) = params.names {
            if !names.contains(&service_type.name) {
                return false;
            }
        }

        true
    }

    /// Efficient query using created_at index with built-in sorting
    pub(crate) fn query_service_type_with_created_at(
        &self,
        params: &ServiceTypeQueryParams,
        service_types: &mut Vec<ServiceType>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Set up iteration range based on sort order
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first (reverse iteration from max to min)
            (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
        } else {
            // Ascending: oldest first (forward iteration from min to max)
            (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
        };

        let mut iter = self.service_types.index("service_types_created_at").iter(reverse, &start_key, &end_key);
        while iter.next() {
            let service_type = iter.value()?;
            if self.matches_service_type_filters(&service_type, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if service_types.len() >= limit as usize {
                    break;
                }
                service_types.push(service_type);
                *count += 1;
            }
        }

        Ok(())
    }

    /// Enhanced query with efficient index-based sorting for composite indexes
    pub(crate) fn query_service_type_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &ServiceTypeQueryParams,
        service_types: &mut Vec<ServiceType>,
        count: &mut u64,
        limit: u64,
        offset: u64,
    ) -> anyhow::Result<()> {
        // Determine sort order from params
        let sort_desc = params.sort_desc.unwrap_or(false);

        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key, reverse) = if sort_desc {
            // Descending: newest first
            (format!("{}-{:9>19}", key_prefix, i64::MAX), format!("{}-{:0>19}", key_prefix, 0), true)
        } else {
            // Ascending: oldest first
            (format!("{}-{:0>19}", key_prefix, 0), format!("{}-{:9>19}", key_prefix, i64::MAX), false)
        };
        let mut iter = self.service_types.index(index_name).iter(reverse, &start_key, &end_key);
        while iter.next() {
            let service_type = iter.value()?;
            if self.matches_service_type_filters(&service_type, &params) {
                if *count < offset {
                    *count += 1;
                    continue;
                }
                if service_types.len() >= limit as usize {
                    break;
                }
                service_types.push(service_type);
                *count += 1;
            }
        }

        Ok(())
    }

    pub(crate) fn count_service_type_with_index(
        &self,
        index_name: &str,
        key_prefix: &str,
        params: &ServiceTypeQueryParams,
        count: &mut u64,
    ) -> anyhow::Result<()> {
        // Composite indexes already include created_at in the key, so we can use reverse iteration
        let (start_key, end_key) =  (
            format!("{}-{:0>19}", key_prefix, 0),
            format!("{}-{:9>19}", key_prefix, i64::MAX),
        );

        let mut iter = self.service_types.index(index_name).iter(false, &start_key, &end_key);
        while iter.next() {
            let service_type = iter.value()?;
            if self.matches_service_type_filters(&service_type, &params) {
                *count += 1;
            }
        }

        Ok(())
    }
}
